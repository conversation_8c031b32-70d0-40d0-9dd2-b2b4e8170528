{"name": "omd-b2b-vite", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/poppins": "^5.2.6", "framer-motion": "^12.17.3", "keen-slider": "^6.8.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}