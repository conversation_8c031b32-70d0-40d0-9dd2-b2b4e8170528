import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Navbar from "./components/Navbar";
import Card from "./components/Card";
import Footer from "./components/Footer";
import LeadForm from "./components/LeadForm";
import { Fa<PERSON>ightbulb, Fa<PERSON>sersCog, FaCogs, FaUserGraduate } from "react-icons/fa";
import ServiceVerticals from "./components/ServiceVerticals";
import TestimonialSlider from "./components/TestimonialSlider";
import { motion } from "framer-motion";
import ServiceSlider from "./components/ServiceSlider";
import Slideshow from "./components/Slideshow";
import "keen-slider/keen-slider.min.css";




// ✅ Page animation wrapper
function PageWrapper({ children }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -40 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
}

function App() {
  return (
    <div className="min-h-screen flex flex-col bg-neutral-50 text-text-primary">
      <Router>
        <Navbar />

        <div className="flex-grow">
          <Routes>

            {/* Home */}
            <Route
              path="/"
              element={
                <PageWrapper>
                  <Slideshow />
                  <div className="space-y-12">
                    
                    {/* Hero Section */}
                    {/* <div className="p-8 bg-blue-50 text-center space-y-6">
                      <h2 className="text-4xl font-extrabold text-blue-700">
                        Empowering Indian MSMEs with 30+ Years of Excellence
                      </h2>
                      <p className="text-lg text-gray-700 max-w-xl mx-auto">
                        We help businesses grow through strategic consulting, industrial support, and skill development.
                        Transform your MSME into a purpose-driven organization with our comprehensive B2B platform.
                      </p>
                      <button className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                        Get Started
                      </button>
                    </div> */}
                    

                    {/* Service Cards */}
                    <div className="px-6 py-16 bg-white">
                      <h2 className="font-heading text-3xl font-bold text-center text-text-primary mb-12">Our Service Verticals</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
                        <Card
                          title="Business Development"
                          description={
                            <>
                              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left mx-auto w-fit">
                                <li>Growth Strategy</li>
                                <li>Profitability Transformation</li>
                                <li>Marketing Optimization</li>
                                <li>Leadership Building</li>
                              </ul>
                              <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                            </>
                          }
                          icon={<FaLightbulb />}
                        />
                        <Card
                          title="Outsourcing & Facilitation"
                          description={
                            <>
                              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left mx-auto w-fit">
                                <li>Sales Outsourcing</li>
                                <li>Marketing Outsourcing</li>
                                <li>BOT Implementation</li>
                                <li>B2B Ecommerce Support</li>
                              </ul>
                              <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                            </>
                          }
                          icon={<FaUsersCog />}
                        />
                        <Card
                          title="Industrial Equipment Supply"
                          description={
                            <>
                              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left mx-auto w-fit">
                                <li>Pumping Equipment</li>
                                <li>Aeration Systems</li>
                                <li>RO Plants</li>
                                <li>Control Instruments</li>
                              </ul>
                              <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                            </>
                          }
                          icon={<FaCogs />}
                        />
                        <Card
                          title="Individual Empowerment"
                          description={
                            <>
                              <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left mx-auto w-fit">
                                <li>Project Internships</li>
                                <li>Mentoring</li>
                                <li>Career Development</li>
                                <li>Training & Placement</li>
                              </ul>
                              <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                            </>
                          }
                          icon={<FaUserGraduate />}
                        />
                      </div>
                    </div>

                    {/* CTA Banner */}
                    <div className="bg-gray-100 text-gray-800 text-center p-8 rounded mx-6 my-10">
                      <h2 className="text-2xl font-bold mb-2">Ready to grow your MSME with us?</h2>
                      <p className="mb-4 text-gray-600">
                        Let’s take the first step together. Join our programs and accelerate your business growth.
                      </p>
                      <button className="bg-blue-600 text-white px-6 py-2 rounded font-semibold hover:bg-blue-700 transition">
                        Contact Us
                      </button>
                    </div>

                    {/* Lead Form + Testimonials */}
                    <LeadForm />
                    <TestimonialSlider />
                  </div>
                </PageWrapper>
              }
            />

            {/* About */}
            <Route
              path="/about"
              element={
                <PageWrapper>
                  <div className="bg-blue-50 py-16 px-6 md:px-16 text-center">
                    <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
                      Empowering Indian MSMEs for Three Decades
                    </h1>
                    <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
                      Marketing Cafe stands as a beacon of business growth enablement for Indian MSMEs. With our
                      proprietary OMD-B2B platform, we’ve successfully transformed countless organizations through
                      strategic consulting, outsourcing solutions, and comprehensive skill development programs.
                    </p>
                    <p className="text-md text-gray-600 max-w-2xl mx-auto">
                      Our vision extends beyond traditional business support — we’re committed to creating
                      purpose-driven organizations that not only achieve sustainable growth but also contribute to
                      employment generation across India.
                    </p>
                  </div>
                </PageWrapper>
              }
            />

            {/* Programs */}
            <Route
              path="/programs"
              element={
                <PageWrapper>
                  <div>
                    <div className="text-center py-10 bg-blue-50">
                      <h1 className="text-3xl font-bold text-blue-700">Our Programs</h1>
                      <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
                        Explore our four key verticals designed to help MSMEs grow and thrive.
                      </p>
                    </div>
                    <ServiceVerticals />
                  </div>
                </PageWrapper>
              }
            />

            {/* Hire Page */}
            <Route
              path="/hire"
              element={
                <PageWrapper>
                  <div className="p-6 space-y-12 text-center">
                    <div className="space-y-4">
                      <h1 className="text-3xl font-bold text-blue-700">Hire Contract Force</h1>
                      <p className="text-gray-700 max-w-2xl mx-auto">
                        This page is coming soon. We're building a comprehensive platform for hiring specialized
                        contract professionals.
                      </p>
                    </div>

                    <div className="bg-gray-100 p-6 rounded shadow max-w-xl mx-auto space-y-2">
                      <h2 className="text-xl font-semibold text-blue-700">MC - Marketing Cafe</h2>
                      <p className="text-sm text-gray-700">OMD-B2B Platform</p>
                      <p className="text-sm text-gray-600">
                        Empowering Indian MSMEs with 30+ years of experience in business growth, skill development,
                        and industrial support through comprehensive solutions.
                      </p>
                      <div className="pt-2 text-sm text-gray-800">
                        <p>📞 +91 (XXX) XXX-XXXX</p>
                        <p>📧 <EMAIL></p>
                        <p>🌐 Pan-India Operations</p>
                      </div>
                    </div>
                  </div>
                </PageWrapper>
              }
            />

            {/* Login */}
            <Route
              path="/login"
              element={
                <PageWrapper>
                  <div className="p-6 text-center text-gray-700 text-lg">Login Page</div>
                </PageWrapper>
              }
            />
          </Routes>
        </div>

        <Footer />
      </Router>
    </div>
  );
}

export default App;
