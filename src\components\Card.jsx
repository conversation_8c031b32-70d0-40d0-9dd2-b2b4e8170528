import { motion } from "framer-motion";

function Card({ title, description, icon, titleColor = "text-text-primary" }) {
  return (
    <div className="group p-8 border border-neutral-200 rounded-xl shadow-soft hover:shadow-strong bg-white text-center transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
      {/* Background gradient on hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <motion.div
        whileHover={{ scale: 1.15, rotate: 5 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
        className="relative z-10 text-5xl text-brand-600 mb-6 flex justify-center"
      >
        <div className="p-4 bg-brand-50 rounded-full group-hover:bg-brand-100 transition-colors duration-300">
          {icon}
        </div>
      </motion.div>

      <h3 className={`relative z-10 font-heading text-xl font-bold ${titleColor} mb-4 group-hover:text-brand-700 transition-colors duration-300`}>
        {title}
      </h3>

      <div className="relative z-10 text-text-secondary leading-relaxed group-hover:text-text-primary transition-colors duration-300">
        {description}
      </div>

      {/* Decorative element */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-brand-500 to-brand-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
    </div>
  );
}

export default Card;
