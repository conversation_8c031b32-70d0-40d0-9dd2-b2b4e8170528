import { Link } from "react-router-dom";
import {
  FaFacebook,
  FaTwitter,
  FaLinkedin,
  FaInstagram,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
} from "react-icons/fa";
import logo from "../assets/mclogo.png";

export default function Footer() {
  return (
    <footer className="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-12 sm:py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-10">
          {/* Company Info */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="flex items-center mb-4 sm:mb-6">
              <img
                src={logo}
                alt="Marketing Cafe"
                className="h-10 sm:h-12 w-10 sm:w-12 object-contain mr-3 bg-white rounded-full"
              />
              <div className="flex flex-col">
                <span className="font-bold text-base sm:text-lg !text-white">Marketing Café</span>
                <span className="text-xs sm:text-sm !text-white">OMD‑B2B Platform</span>
              </div>
            </div>
            <p className="mb-3 sm:mb-4 !text-white leading-relaxed text-sm sm:text-base">
              Empowering Indian MSMEs with 30+ years of experience in business growth.
              A business advisory, consulting & facilitating firm registered with the Ministry of MSMEs, Govt. of India.
            </p>
            <p className="text-xs sm:text-sm !text-white">Since 2016 • Trusted by 1000+ MSMEs</p>
          </div>

          {/* Our Services */}
          <div>
            <h3 className="!text-white font-semibold mb-3 sm:mb-4 text-base sm:text-lg">Our Services</h3>
            <ul className="space-y-1 sm:space-y-2 !text-white text-sm sm:text-base">
              <li className="hover:text-orange-300 transition">Outsource Sales</li>
              <li className="hover:text-orange-300 transition">Outsource Marketing</li>
              <li className="hover:text-orange-300 transition">Business Consulting</li>
              <li className="hover:text-orange-300 transition">Productivity Coaching</li>
              <li className="hover:text-orange-300 transition">Supply & Distribution</li>
              <li className="hover:text-orange-300 transition">Portfolio Management</li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="!text-white font-semibold mb-3 sm:mb-4 text-base sm:text-lg">Company</h3>
            <ul className="space-y-1 sm:space-y-2 !text-white text-sm sm:text-base">
              <li><Link to="/about" className="!text-white hover:text-orange-300 transition">About Us</Link></li>
              <li><Link to="/omd-grow-business-platform" className="!text-white hover:text-orange-300 transition">OMD Grow Business Platform</Link></li>
              <li><Link to="/faq" className="!text-white hover:text-orange-300 transition">FAQ’s</Link></li>
              <li><Link to="/sitemap" className="!text-white hover:text-orange-300 transition">Sitemap</Link></li>
              <li><Link to="/blogs" className="!text-white hover:text-orange-300 transition">Blogs</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="!text-white font-semibold mb-4 text-lg">Contact Us</h3>
            <div className="space-y-3 !text-white">
              <p className="flex items-center !text-white">
                <FaPhone className="mr-3 text-orange-400" />
                +91 9717687648
              </p>
              <p className="flex items-center !text-white">
                <FaEnvelope className="mr-3 text-orange-400" />
                <EMAIL>
              </p>
              <p className="flex items-start leading-relaxed">
                <FaMapMarkerAlt className="mr-3 text-orange-400 mt-1 flex-shrink-0" />
                <span className="text-sm !text-white">
                  #A1-506, BHARAT CITY, BHOPURA LONI ROAD,<br />
                  LANDMARK - TEELA MORE POLICE STATION,<br />
                  GHAZIABAD - 201003 (U.P.) INDIA
                </span>
              </p>
            </div>

            {/* Follow Us Heading */}
            <h3 className="mt-6 mb-2 font-semibold !text-white">Follow Us</h3>

            {/* Social Icons */}
            <div className="flex space-x-4 !text-white text-xl">
              <a href="#" className="hover:text-orange-400"><FaTwitter /></a>
              <a href="#" className="hover:text-orange-400"><FaLinkedin /></a>
              <a href="#" className="hover:text-orange-400"><FaFacebook /></a>
              <a href="#" className="hover:text-orange-400"><FaInstagram /></a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="text-center text-sm !text-white mt-12 border-t border-gray-700 pt-6">
          © 2025 <span className="text-orange-400 font-semibold">Marketing Cafe</span>. All Rights Reserved.
          <div className="mt-1 text-xs !text-white">
            Empowering MSMEs • Registered with Ministry of MSMEs, Govt. of India
          </div>
        </div>
      </div>
    </footer>
  );
}
