import { useState, useEffect } from "react";
import joinImage from "../assets/join-with-us.png";

export default function LeadForm() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    company: '',
    service: '',
    message: ''
  });

  const [imageLoaded, setImageLoaded] = useState(false);

  // Check if image loads
  const checkImageLoad = () => {
    const img = new Image();
    img.onload = () => setImageLoaded(true);
    img.onerror = () => {
      console.warn('Background image failed to load:', joinImage);
      setImageLoaded(false);
    };
    img.src = joinImage;
  };

  // Check image on component mount
  useEffect(() => {
    checkImageLoad();
  }, []);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Add form submission logic here
    alert('Thank you for your inquiry! We will get back to you soon.');
    // Reset form
    setFormData({
      fullName: '',
      email: '',
      company: '',
      service: '',
      message: ''
    });
  };

  return (
    <div className="relative py-12 sm:py-16 md:py-20 px-4 bg-gradient-to-br from-blue-600 to-blue-800">
      {/* Background Image with Blur */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: imageLoaded
            ? `url(${joinImage})`
            : `url('/join-with-us.png')`,
          filter: 'blur(2px)',
        }}
      ></div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/60"></div>

      {/* Form Container */}
      <div className="relative z-10 max-w-2xl mx-auto bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-lg">
        <h2 className="text-2xl sm:text-3xl font-bold text-center text-blue-700 mb-4 sm:mb-6">Get Started Today</h2>

        <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
          <input
            type="text"
            name="fullName"
            placeholder="Full Name"
            value={formData.fullName}
            onChange={handleChange}
            className="w-full p-2 sm:p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
            required
          />
          <input
            type="email"
            name="email"
            placeholder="Email Address"
            value={formData.email}
            onChange={handleChange}
            className="w-full p-2 sm:p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
            required
          />
          <input
            type="text"
            name="company"
            placeholder="Company Name"
            value={formData.company}
            onChange={handleChange}
            className="w-full p-2 sm:p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
          />
          <select
            name="service"
            value={formData.service}
            onChange={handleChange}
            className="w-full p-2 sm:p-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
            required
          >
            <option value="">Select a service</option>
            <option value="Business Development">Business Development</option>
            <option value="Outsourcing">Outsourcing & Facilitation</option>
            <option value="Equipment Supply">Industrial Equipment Supply</option>
            <option value="Empowerment">Individual Empowerment</option>
          </select>
          <textarea
            name="message"
            placeholder="Message"
            value={formData.message}
            onChange={handleChange}
            className="w-full p-2 sm:p-3 border border-gray-300 rounded h-24 sm:h-28 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base resize-none"
            required
          />

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 sm:py-3 rounded hover:bg-blue-700 transition duration-200 font-semibold text-sm sm:text-base"
          >
            Send Inquiry
          </button>
        </form>
      </div>
    </div>
  );
}
