import { useState } from "react";
import { Link } from "react-router-dom";
import logo from "../assets/mclogo.png";
import { FaBars, FaTimes } from "react-icons/fa";

function Navbar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="bg-white/95 backdrop-blur-sm shadow-soft border-b border-neutral-100 p-4 text-base sticky top-0 z-50">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Left: Logo */}
        <Link to="/" className="flex items-center group">
          <img src={logo} alt="Marketing Cafe" className="h-8 sm:h-10 w-auto mr-2 sm:mr-3 transition-transform group-hover:scale-105" />
          <div className="flex flex-col">
            <span className="text-black font-heading font-bold text-base sm:text-lg">Marketing Café</span>
            <span className="text-xs sm:text-sm text-black font-medium">OMD‑B2B Platform</span>
          </div>
        </Link>

        {/* Center: Desktop Navigation */}
        <div className="hidden md:flex items-center justify-center flex-1 space-x-8">
          <NavLink to="/">Home</NavLink>
          <NavLink to="/about">About</NavLink>
          <NavLink to="/programs">Our Programs</NavLink>
          <NavLink to="/hire">Hire Contract Force</NavLink>
        </div>

        {/* Right: Action Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          <Link to="/login" className="!text-black hover:!text-blue-600 px-6 py-2.5 rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 font-semibold border-2 border-black hover:border-blue-600">
            Login
          </Link>
          <Link to="/get-started" className="!text-black hover:!text-blue-600 px-6 py-2.5 rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 font-semibold border-2 border-black hover:border-blue-600">
            Get Started
          </Link>
        </div>

        {/* Mobile menu button */}
        <button
          className="md:hidden text-neutral-600 hover:text-brand-600 p-2 rounded-lg hover:bg-neutral-50 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden mt-4 py-4 bg-white/95 backdrop-blur-sm rounded-lg shadow-medium border border-neutral-100 space-y-2">
          <MobileNavLink to="/" onClick={() => setIsOpen(false)}>Home</MobileNavLink>
          <MobileNavLink to="/about" onClick={() => setIsOpen(false)}>About</MobileNavLink>
          <MobileNavLink to="/programs" onClick={() => setIsOpen(false)}>Our Programs</MobileNavLink>
          <MobileNavLink to="/hire" onClick={() => setIsOpen(false)}>Hire Contract Force</MobileNavLink>
          <div className="flex flex-col space-y-2 mx-4">
            <Link to="/login" onClick={() => setIsOpen(false)} className="block !text-black hover:!text-blue-600 px-4 py-3 rounded-lg text-center font-semibold hover:bg-blue-50 transition-all duration-200 border-2 border-black hover:border-blue-600 shadow-md">
              Login
            </Link>
            <Link to="/get-started" onClick={() => setIsOpen(false)} className="block !text-black hover:!text-blue-600 px-4 py-3 rounded-lg text-center font-semibold hover:bg-blue-50 transition-all duration-200 border-2 border-black hover:border-blue-600 shadow-md">
              Get Started
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}

// Helper components
function NavLink({ to, children, className = "" }) {
  return (
    <Link to={to} className={`!text-black hover:!text-blue-600 font-medium transition-colors duration-200 relative group ${className}`}>
      {children}
      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"></span>
    </Link>
  );
}

function MobileNavLink({ to, children, className = "", onClick }) {
  return (
    <Link to={to} className={`block px-4 py-3 !text-black hover:bg-neutral-50 hover:!text-blue-600 rounded-lg mx-2 transition-colors duration-200 font-medium ${className}`} onClick={onClick}>
      {children}
    </Link>
  );
}

export default Navbar;
