import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const slides = [
  {
    title: "Business Consulting",
    description: "Boost your MSME with strategic growth and leadership advisory.",
    img: "https://images.unsplash.com/photo-1551836022-4c4c79ecde16",
  },
  {
    title: "B2B Outsourcing",
    description: "Optimize operations through smart outsourcing & BOT models.",
    img: "https://images.unsplash.com/photo-1605902711622-cfb43c4437e1",
  },
  {
    title: "Equipment Supply",
    description: "RO Plants, Pumps, Aeration systems & more delivered across India.",
    img: "https://images.unsplash.com/photo-1581091012184-7c02a20a41f1",
  },
  {
    title: "Skill Development",
    description: "Empowering individuals through internships & mentorship programs.",
    img: "https://images.unsplash.com/photo-1522075469751-3a6694fb2f61",
  },
];

export default function ServiceSlider() {
  const settings = {
    dots: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 4000,
    arrows: false,
    pauseOnHover: false,
  };

  return (
    <div className="py-10 bg-white">
      <Slider {...settings}>
        {slides.map((item, idx) => (
          <div key={idx} className="text-center px-4">
            <img
              src={item.img}
              alt={item.title}
              className="h-60 w-full object-cover rounded-md shadow-md mx-auto"
            />
            <h3 className="text-xl font-bold text-blue-700 mt-4">{item.title}</h3>
            <p className="text-gray-600 max-w-md mx-auto">{item.description}</p>
          </div>
        ))}
      </Slider>
    </div>
  );
}
