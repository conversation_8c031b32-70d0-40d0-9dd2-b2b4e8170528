import Card from "./Card";
import { FaLightbulb, FaUsersCog, <PERSON>aCogs, FaUserGraduate } from "react-icons/fa";
import omdB2bImage from "../assets/omd-b2b.png";

function ServiceVerticals() {
  return (
    <div className="px-6 my-10">
      <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">Our Service Verticals</h2>
      <div className="flex flex-col lg:flex-row gap-12 lg:gap-16 items-center">
        {/* Left Side - OMD-B2B Image */}
        <div className="lg:w-1/3 flex justify-center lg:justify-start">
          <img
            src={omdB2bImage}
            alt="OMD-B2B Platform"
            className="w-full max-w-sm h-auto object-contain"
          />
        </div>

        {/* Right Side - Service Cards */}
        <div className="lg:w-2/3">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            <Card
              title="Business Development"
              description={
                <>
                  <div className="flex justify-center">
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left">
                      <li>Growth Strategy</li>
                      <li>Profitability Transformation</li>
                      <li>Marketing Optimization</li>
                      <li>Leadership Building</li>
                    </ul>
                  </div>
                  <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                </>
              }
              icon={<FaLightbulb />}
              titleColor="text-white"
            />
            <Card
              title="Outsourcing & Facilitation"
              description={
                <>
                  <div className="flex justify-center">
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left">
                      <li>Sales Outsourcing</li>
                      <li>Marketing Outsourcing</li>
                      <li>BOT Implementation</li>
                      <li>B2B Ecommerce Support</li>
                    </ul>
                  </div>
                  <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                </>
              }
              icon={<FaUsersCog />}
              titleColor="text-white"
            />
            <Card
              title="Industrial Equipment Supply"
              description={
                <>
                  <div className="flex justify-center">
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left">
                      <li>Pumping Equipment</li>
                      <li>Aeration Systems</li>
                      <li>RO Plants</li>
                      <li>Control Instruments</li>
                    </ul>
                  </div>
                  <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                </>
              }
              icon={<FaCogs />}
              titleColor="text-white"
            />
            <Card
              title="Individual Empowerment"
              description={
                <>
                  <div className="flex justify-center">
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 text-left">
                      <li>Project Internships</li>
                      <li>Mentoring</li>
                      <li>Career Development</li>
                      <li>Training & Placement</li>
                    </ul>
                  </div>
                  <button className="mt-4 text-sm text-blue-600 hover:underline">Learn More</button>
                </>
              }
              icon={<FaUserGraduate />}
              titleColor="text-white"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ServiceVerticals;
