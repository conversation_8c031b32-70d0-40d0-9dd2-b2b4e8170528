import { useKeenSlider } from "keen-slider/react";
import "keen-slider/keen-slider.min.css";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaLightbulb, FaUsersCog, FaCogs, FaUserGraduate, FaChevronLeft, FaChevronRight } from "react-icons/fa";
import businessConsultingImg from "../assets/business consulting.jpg";
import careerDevelopmentImg from "../assets/carrer development.jpeg";
import omdB2bImg from "../assets/omd-b2b.png";

const Slideshow = () => {
  const [currentSlide, setCurrentSlide] = useState(0);



  const [sliderRef, instanceRef] = useKeenSlider({
    loop: true,
    duration: 1200, // 1.2 second transition duration
    slides: { perView: 1 },
    slideChanged(slider) {
      setCurrentSlide(slider.track.details.rel);
    },
    created() {
      // Add custom CSS for smooth transitions with parallax effect
      const slides = document.querySelectorAll('.keen-slider__slide');
      slides.forEach((slide) => {
        slide.style.transition = 'transform 1.2s cubic-bezier(0.23, 1, 0.32, 1)';
        slide.style.transformOrigin = 'center center';
      });
    }
  });

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(() => {
      if (instanceRef.current) {
        instanceRef.current.next();
      }
    }, 6000);
    return () => clearInterval(interval);
  }, [instanceRef]);

  const slides = [
    {
      id: 1,
      type: "hero",
      title: "Empowering Indian MSMEs for a Better Tomorrow",
      subtitle: "30+ Years of Excellence",
      description: "Transform your marketing department into a revenue-generating engine with our proven expertise in business growth and MSME empowerment.",
      cta: "Get Started Today",
      background: "from-brand-700 via-brand-800 to-brand-900",
      textColor: "text-white"
    },
    {
      id: 2,
      type: "service",
      title: "Business Development Consulting",
      subtitle: "Strategic Growth Solutions",
      description: "MSME growth strategies, product-to-profitability transformation, marketing optimization, and leadership capacity building.",
      features: ["Growth Strategy", "Profitability Transformation", "Marketing Optimization", "Leadership Building"],
      cta: "Learn More",
      icon: <FaLightbulb />,
      background: "from-accent-orange/10 to-accent-orange/5",
      textColor: "text-text-primary",
      accentColor: "accent-orange"
    },
    {
      id: 3,
      type: "service",
      title: "Outsourcing & Facilitation",
      subtitle: "Complete Business Solutions",
      description: "Sales function outsourcing, marketing function outsourcing, and full department BOT model implementation.",
      features: ["Sales Outsourcing", "Marketing Outsourcing", "BOT Implementation", "B2B Ecommerce Support"],
      cta: "Explore Services",
      icon: <FaUsersCog />,
      background: "from-brand-50 to-brand-100/50",
      textColor: "text-text-primary",
      accentColor: "brand-600"
    },
    {
      id: 4,
      type: "service",
      title: "Industrial Equipment Supply",
      subtitle: "Quality Equipment Solutions",
      description: "Comprehensive supply of pumping equipment, aeration systems, commercial RO plants, and measurement instruments.",
      features: ["Pumping Equipment", "Aeration Systems", "Commercial RO Plants", "Control Instruments"],
      cta: "View Products",
      icon: <FaCogs />,
      background: "from-accent-green/10 to-accent-green/5",
      textColor: "text-text-primary",
      accentColor: "accent-green"
    },
    {
      id: 5,
      type: "service",
      title: "Individual Empowerment",
      subtitle: "Career Development Programs",
      description: "Live project internships, mentoring, coaching, and comprehensive career development programs.",
      features: ["Project Internships", "Mentoring Programs", "Career Development", "Training & Placement"],
      cta: "Join Programs",
      icon: <FaUserGraduate />,
      background: "from-accent-purple/10 to-accent-purple/5",
      textColor: "text-text-primary",
      accentColor: "accent-purple"
    }
  ];

  const nextSlide = () => {
    if (instanceRef.current) {
      instanceRef.current.next();
    }
  };

  const prevSlide = () => {
    if (instanceRef.current) {
      instanceRef.current.prev();
    }
  };

  return (
    <div className="relative">
      <div ref={sliderRef} className="keen-slider">
        {slides.map((slide) => (
          <div key={slide.id} className="keen-slider__slide">
            <AnimatePresence mode="wait">
              <motion.div
                key={`slide-${slide.id}-${currentSlide}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.6, ease: "easeInOut" }}
                className="w-full h-full"
              >
            {slide.type === "hero" ? (
              // Hero Slide
              <div className={`bg-gradient-to-br ${slide.background} ${slide.textColor} relative overflow-hidden`}>
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute inset-0 bg-gradient-to-br from-brand-600/30 to-brand-800/30"></div>
                  <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
                </div>

                <div className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 md:py-24 lg:py-32 relative z-10 min-h-[60vh] sm:min-h-[70vh] flex items-center justify-center">
                  <div className="max-w-4xl text-center">
                    {/* Excellence Highlight */}
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 }}
                      className="mb-3 sm:mb-4"
                    >
                      <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-yellow-400 tracking-wide drop-shadow-lg">
                        30+ Years of Excellence
                      </h3>
                    </motion.div>

                    <motion.h1
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                      className="font-heading text-2xl sm:text-3xl md:text-4xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 text-white leading-tight"
                    >
                      {slide.title}
                    </motion.h1>

                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.4 }}
                      className="text-sm sm:text-base md:text-lg lg:text-xl mb-6 sm:mb-8 md:mb-10 text-blue-100 leading-relaxed max-w-3xl mx-auto px-4"
                    >
                      {slide.description}
                    </motion.p>

                    <motion.div
                      initial={{ opacity: 0, y: 30, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ duration: 0.8, delay: 0.6, type: "spring", stiffness: 120 }}
                      className="flex flex-col sm:flex-row gap-4 justify-center"
                    >
                      <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
                        {slide.cta}
                      </button>
                      <button className="border-2 border-blue-600 text-blue-600 bg-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300">
                        Learn More
                      </button>
                    </motion.div>
                  </div>
                </div>
              </div>
            ) : (
              // Service Slide
              <div className={`bg-gradient-to-br ${slide.background} min-h-[60vh] sm:min-h-[70vh] flex items-center justify-center`}>
                <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-16">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center max-w-6xl mx-auto">
                    {/* Left: Content */}
                    <motion.div
                      initial={{ opacity: 0, x: -80 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.8, ease: "easeOut" }}
                      className="space-y-4 sm:space-y-6 text-center lg:text-left"
                    >
                      <motion.div
                        className={`text-4xl sm:text-5xl lg:text-6xl mb-3 sm:mb-4 ${
                          slide.accentColor === 'accent-orange' ? 'text-orange-500' :
                          slide.accentColor === 'brand-600' ? 'text-blue-600' :
                          slide.accentColor === 'accent-green' ? 'text-green-500' :
                          'text-purple-500'
                        }`}
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ duration: 0.8, delay: 0.2, type: "spring", stiffness: 200 }}
                      >
                        {slide.icon}
                      </motion.div>

                      <div>
                        <h3 className={`text-sm font-semibold uppercase tracking-wide mb-2 ${
                          slide.accentColor === 'accent-orange' ? 'text-orange-500' :
                          slide.accentColor === 'brand-600' ? 'text-blue-600' :
                          slide.accentColor === 'accent-green' ? 'text-green-500' :
                          'text-purple-500'
                        }`}>
                          {slide.subtitle}
                        </h3>
                        <h2 className="font-heading text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4 lg:px-0">
                          {slide.title}
                        </h2>
                      </div>

                      <p className="text-sm sm:text-base lg:text-lg text-gray-600 leading-relaxed px-4 lg:px-0">
                        {slide.description}
                      </p>

                      <div className="grid grid-cols-2 gap-3">
                        {slide.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              slide.accentColor === 'accent-orange' ? 'bg-orange-500' :
                              slide.accentColor === 'brand-600' ? 'bg-blue-600' :
                              slide.accentColor === 'accent-green' ? 'bg-green-500' :
                              'bg-purple-500'
                            }`}></div>
                            <span className="text-gray-600 font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>

                      <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
                        {slide.cta}
                      </button>
                    </motion.div>

                    {/* Right: Image Area */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8, delay: 0.3, type: "spring", stiffness: 100 }}
                      className="flex justify-center"
                    >
                      <div className="w-72 h-72 sm:w-80 sm:h-80 lg:w-96 lg:h-96 bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 group hover:shadow-2xl transition-all duration-500">
                        <div className="w-full h-full overflow-hidden">
                          {/* Service images with zoom effects */}
                          {slide.title === "Business Development Consulting" ? (
                            <motion.img
                              src={businessConsultingImg}
                              alt="Business Development Consulting - Innovation and Strategy"
                              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                              initial={{ scale: 1.1, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.8, ease: "easeOut" }}
                            />
                          ) : slide.title === "Individual Empowerment" ? (
                            <motion.img
                              src={careerDevelopmentImg}
                              alt="Individual Empowerment - Career Development Programs"
                              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                              initial={{ scale: 1.1, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.8, ease: "easeOut" }}
                            />
                          ) : slide.title === "Outsourcing & Facilitation" ? (
                            <motion.img
                              src={omdB2bImg}
                              alt="Outsourcing & Facilitation - OMD B2B Services"
                              className="w-full h-full object-contain p-4 bg-white transition-transform duration-500 hover:scale-105"
                              initial={{ scale: 0.9, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.8, ease: "easeOut" }}
                            />
                          ) : slide.title === "Industrial Equipment Supply" ? (
                            <motion.img
                              src={omdB2bImg}
                              alt="Industrial Equipment Supply - OMD B2B Solutions"
                              className="w-full h-full object-contain p-4 bg-white transition-transform duration-500 hover:scale-105"
                              initial={{ scale: 0.9, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.8, ease: "easeOut" }}
                            />
                          ) : (
                            <motion.div
                              className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100"
                              initial={{ scale: 0.9, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 1, ease: "easeOut" }}
                            >
                              <motion.div
                                className={`text-6xl mb-4 ${
                                  slide.accentColor === 'accent-orange' ? 'text-orange-500' :
                                  slide.accentColor === 'brand-600' ? 'text-blue-600' :
                                  slide.accentColor === 'accent-green' ? 'text-green-500' :
                                  'text-purple-500'
                                }`}
                                initial={{ scale: 0, rotate: -180 }}
                                animate={{ scale: 1, rotate: 0 }}
                                transition={{ duration: 0.8, delay: 0.5, type: "spring", stiffness: 200 }}
                              >
                                {slide.icon}
                              </motion.div>
                              <div className="text-center px-6">
                                <h4 className="font-semibold text-gray-700 mb-2">Service Image</h4>
                                <p className="text-sm text-gray-500">Service image placeholder</p>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            )}
              </motion.div>
            </AnimatePresence>
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <motion.button
        onClick={prevSlide}
        className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-blue-600 p-2 sm:p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-10"
        whileHover={{ scale: 1.1, x: -2 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <FaChevronLeft size={16} className="sm:hidden" />
        <FaChevronLeft size={20} className="hidden sm:block" />
      </motion.button>

      <motion.button
        onClick={nextSlide}
        className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-brand-600 p-2 sm:p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-10"
        whileHover={{ scale: 1.1, x: 2 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <FaChevronRight size={16} className="sm:hidden" />
        <FaChevronRight size={20} className="hidden sm:block" />
      </motion.button>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => instanceRef.current?.moveToIdx(index)}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              currentSlide === index
                ? 'bg-white shadow-lg'
                : 'bg-white/50 hover:bg-white/70'
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>
    </div>
  );
};

export default Slideshow;
