import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "CEO, GrowthTech",
    feedback: "OMD-B2B has been a game changer for our MSME. We scaled our outreach in just 3 months!",
  },
  {
    name: "<PERSON><PERSON>",
    role: "Founder, SkillHub",
    feedback: "Thanks to Marketing Cafe, we got access to amazing mentorship and business strategy.",
  },
  {
    name: "<PERSON><PERSON>",
    role: "Managing Director, EquipMart",
    feedback: "Their equipment supply and service models are rock solid. Great support & delivery!",
  },
];

export default function TestimonialSlider() {
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    autoplay: true,
    autoplaySpeed: 4000,
    slidesToShow: 1,
    slidesToScroll: 1,
  };

  return (
    <div className="bg-white py-12 px-4 md:px-12">
      <h2 className="text-3xl font-bold text-center text-blue-800 mb-8">
        What Our Clients Say
      </h2>
      <Slider {...settings}>
        {testimonials.map((item, index) => (
          <div key={index} className="p-6 text-center border rounded shadow-sm">
            <p className="text-gray-700 italic mb-4">“{item.feedback}”</p>
            <h4 className="text-blue-700 font-semibold">{item.name}</h4>
            <p className="text-sm text-gray-500">{item.role}</p>
          </div>
        ))}
      </Slider>
    </div>
  );
}
