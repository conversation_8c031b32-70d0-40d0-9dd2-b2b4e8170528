@import "tailwindcss";
@import "slick-carousel/slick/slick.css";
@import "slick-carousel/slick/slick-theme.css";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Root CSS Variables for Color Scheme */
:root {
  /* Primary Brand Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* Accent Colors */
  --color-accent-orange: #f97316;
  --color-accent-green: #10b981;
  --color-accent-purple: #8b5cf6;
  --color-accent-red: #ef4444;

  /* Text Colors */
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-text-light: #94a3b8;
  --color-text-white: #ffffff;

  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-dark: #0f172a;
}

/* Base Typography */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--color-text-primary);
  line-height: 1.6;
  font-weight: 400;
}

/* Heading Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-text-primary);
}

h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-text-primary);
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

/* Responsive Typography */
@media (min-width: 640px) {
  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.875rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

@media (min-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3rem;
  }

  h2 {
    font-size: 2.25rem;
  }

  h3 {
    font-size: 1.75rem;
  }
}

/* Paragraph and Text */
p {
  color: var(--color-text-secondary);
  font-size: 1rem;
  line-height: 1.7;
}

.text-lead {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  line-height: 1.7;
}

.text-small {
  font-size: 0.875rem;
  color: var(--color-text-tertiary);
}

/* Link Styles */
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-700);
}

/* Button Styles */
.btn-primary {
  background-color: var(--color-primary-600);
  color: var(--color-text-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--color-primary-700);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary-600);
  border: 2px solid var(--color-primary-600);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--color-primary-600);
  color: var(--color-text-white);
}

/* Card Styles */
.card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-secondary-200);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Utility Classes for Colors */
.text-primary { color: var(--color-text-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-tertiary { color: var(--color-text-tertiary) !important; }
.text-light { color: var(--color-text-light) !important; }
.text-brand { color: var(--color-primary-600) !important; }
.text-accent-orange { color: var(--color-accent-orange) !important; }
.text-accent-green { color: var(--color-accent-green) !important; }

.bg-primary { background-color: var(--color-bg-primary) !important; }
.bg-secondary { background-color: var(--color-bg-secondary) !important; }
.bg-tertiary { background-color: var(--color-bg-tertiary) !important; }

/* Responsive Utilities */
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2.5rem;
  }
}

/* Mobile-first responsive spacing */
.section-padding {
  padding: 2rem 0;
}

@media (min-width: 640px) {
  .section-padding {
    padding: 3rem 0;
  }
}

@media (min-width: 768px) {
  .section-padding {
    padding: 4rem 0;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 5rem 0;
  }
}

/* Responsive text sizes */
.text-responsive-sm {
  font-size: 0.875rem;
}

.text-responsive-base {
  font-size: 0.875rem;
}

.text-responsive-lg {
  font-size: 1rem;
}

@media (min-width: 640px) {
  .text-responsive-base {
    font-size: 1rem;
  }

  .text-responsive-lg {
    font-size: 1.125rem;
  }
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 1.25rem;
  }
}
