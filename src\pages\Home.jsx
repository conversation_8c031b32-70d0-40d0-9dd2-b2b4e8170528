import React from 'react';
import { motion } from "framer-motion";
import HeroSection from "../components/HeroSection";
import ServiceVerticals from "../components/ServiceVerticals";
import TestimonialSlider from "../components/TestimonialSlider";
import LeadForm from "../components/LeadForm";
import { Link } from "react-router-dom";

// Reusable section container
function SectionContainer({ title, children, bgColor = "bg-white" }) {
  return (
    <section className={`py-16 px-6 ${bgColor}`}>
      <div className="max-w-7xl mx-auto">
        {title && (
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-center text-gray-800 mb-10"
          >
            {title}
          </motion.h2>
        )}
        {children}
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <div className="bg-white text-gray-900">
      {/* Hero Section */}
      <HeroSection />
      
      {/* About Section */}
      <SectionContainer title="About Us" bgColor="bg-white">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto"
        >
          <p className="text-lg text-gray-700 mb-6">
            Marketing Cafe, with over 30 years of legacy, empowers MSMEs with growth consulting,
            outsourcing, industrial support, and individual empowerment. Through the OMD-B2B platform,
            we provide scalable, impact-driven solutions.
          </p>
          <Link 
            to="/about" 
            className="inline-block text-blue-600 font-semibold hover:text-blue-800 transition"
          >
            Learn more about our journey →
          </Link>
        </motion.div>
      </SectionContainer>
      
      {/* Service Verticals */}
      <ServiceVerticals />
      
      {/* CTA Banner */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className="bg-gray-100 text-gray-800 text-center p-8 md:p-12 rounded-lg mx-6 my-16 max-w-7xl md:mx-auto"
      >
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to grow your MSME with us?</h2>
        <p className="mb-6 text-gray-600 max-w-2xl mx-auto">
          Let's take the first step together. Join our programs and accelerate your business growth.
        </p>
        <Link
          to="/contact"
          className="bg-blue-600 text-white px-8 py-3 rounded-md font-semibold hover:bg-blue-700 transition inline-block"
        >
          Contact Us
        </Link>
      </motion.div>
      
      {/* Lead Form */}
      <LeadForm />
      
      {/* Testimonials */}
      <TestimonialSlider />
    </div>
  );
}
